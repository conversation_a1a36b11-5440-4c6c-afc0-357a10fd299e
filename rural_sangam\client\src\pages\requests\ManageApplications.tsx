import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router";
import { <PERSON>, <PERSON><PERSON>, Badge, LoadingSpinner } from "../../components/common";
import { getVolunteersForRequest, updateVolunteerStatus } from "../../services";
import { useAuth } from "../../hooks/useAuth";
import AuthDebugInfo from "../../components/debug/AuthDebugInfo";

interface VolunteerApplication {
  volunteer: {
    _id: string;
    name: string;
    email: string;
    skills?: string[];
  };
  status: "pending" | "accepted" | "rejected";
  appliedAt?: string; // Backend might not have this field
}

const ManageApplications: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [applications, setApplications] = useState<VolunteerApplication[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [updatingStatus, setUpdatingStatus] = useState<string | null>(null);
  const [statusUpdateError, setStatusUpdateError] = useState<string | null>(
    null
  );
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    const fetchApplications = async () => {
      if (!id) return;

      // Check if user is authenticated and has school profile
      if (!user || !user.user || user.user.role !== "school") {
        setError("You must be logged in as a school to view applications.");
        setLoading(false);
        return;
      }

      if (!user.profile) {
        setError(
          "School profile not found. Please complete your profile first."
        );
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        console.log("Fetching applications for request ID:", id);
        console.log("Current user:", user.user);
        console.log("School profile:", user.profile);

        // First, let's try to get the request details to verify ownership
        // This is a workaround for the server-side authorization bug
        try {
          const data = await getVolunteersForRequest(id);
          console.log("Applications data received:", data);
          setApplications(data || []);
        } catch (authError: any) {
          // If we get an authorization error, it's likely due to the server bug
          // Let's try to get the school's own requests and find this one
          if (authError.message?.includes("Not authorized")) {
            console.log(
              "Authorization error detected, trying alternative approach..."
            );

            try {
              // Import getMyRequests to check if this request belongs to the school
              const { getMyRequests } = await import("../../services");
              const myRequests = await getMyRequests();
              const myRequest = myRequests.find((req: any) => req._id === id);

              if (myRequest) {
                // If we found the request in our own requests, use its volunteers data
                console.log(
                  "Found request in school's own requests:",
                  myRequest
                );
                setApplications(myRequest.volunteers || []);
              } else {
                // This request doesn't belong to this school
                setError(
                  "You are not authorized to view applications for this request. This request may not belong to your school."
                );
              }
            } catch (fallbackError) {
              console.error("Fallback approach failed:", fallbackError);
              setError(
                "Unable to load applications. There may be a server issue. Please try again later or contact support."
              );
            }
          } else {
            // Re-throw other types of errors
            throw authError;
          }
        }
      } catch (err: any) {
        console.error("Error fetching applications:", err);
        console.error("Error message:", err.message);
        console.error("Error response:", err.response?.data);

        // More specific error messages
        if (err.message?.includes("Request not found")) {
          setError("Request not found. It may have been deleted.");
        } else if (!err.message?.includes("Not authorized")) {
          // Only show generic error if it's not an authorization issue (already handled above)
          setError(`Failed to load applications: ${err.message}`);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchApplications();
  }, [id, user]);

  const handleStatusUpdate = async (
    volunteerId: string,
    status: "accepted" | "rejected"
  ) => {
    if (!id) return;

    try {
      setUpdatingStatus(volunteerId);
      setStatusUpdateError(null);
      setSuccessMessage(null);

      await updateVolunteerStatus(id, volunteerId, status);

      // Update local state
      setApplications((prev) =>
        prev.map((app) =>
          app.volunteer._id === volunteerId ? { ...app, status } : app
        )
      );

      // Show success message
      const volunteerName =
        applications.find((app) => app.volunteer._id === volunteerId)?.volunteer
          .name || "Volunteer";
      setSuccessMessage(`${volunteerName} has been ${status} successfully.`);

      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error: any) {
      console.error("Failed to update status:", error);

      // Handle different types of errors
      if (error.message?.includes("Not authorized")) {
        setStatusUpdateError(
          "You are not authorized to update this application. Please ensure you own this request."
        );
      } else if (error.message?.includes("not found")) {
        setStatusUpdateError(
          "Application not found. It may have been removed or the request may have been deleted."
        );
      } else {
        setStatusUpdateError(
          `Failed to ${
            status === "accepted" ? "accept" : "reject"
          } application: ${error.message}`
        );
      }
    } finally {
      setUpdatingStatus(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "accepted":
        return <Badge variant="success">Accepted</Badge>;
      case "rejected":
        return <Badge variant="danger">Rejected</Badge>;
      default:
        return <Badge variant="warning">Pending</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center text-red-600">
          <p>{error}</p>
          <Button onClick={() => navigate(-1)} className="mt-4">
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <Button variant="outline" onClick={() => navigate(-1)} className="mb-4">
          ← Back to Request
        </Button>

        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Manage Applications
        </h1>
        <p className="text-gray-600">
          Review and manage volunteer applications for this request.
        </p>
      </div>

      {/* Information Banner */}
      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-start">
          <svg
            className="w-5 h-5 text-blue-600 mr-2 mt-0.5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <div>
            <p className="text-blue-800 font-medium">Application Management</p>
            <p className="text-blue-700 text-sm mt-1">
              This page shows applications for your volunteer requests. If you
              encounter any authorization issues, the system will automatically
              try alternative methods to load your data.
            </p>
          </div>
        </div>
      </div>

      {/* Debug Info - Remove this after fixing the issue */}
      <div className="mb-6">
        <AuthDebugInfo />
      </div>

      {/* Success Message */}
      {successMessage && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center">
            <svg
              className="w-5 h-5 text-green-600 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
            <p className="text-green-800">{successMessage}</p>
          </div>
        </div>
      )}

      {/* Status Update Error Message */}
      {statusUpdateError && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center">
            <svg
              className="w-5 h-5 text-red-600 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <p className="text-red-800">{statusUpdateError}</p>
            <button
              onClick={() => setStatusUpdateError(null)}
              className="ml-auto text-red-600 hover:text-red-800"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {applications.length === 0 ? (
        <Card>
          <div className="text-center py-12">
            <svg
              className="w-12 h-12 text-gray-400 mx-auto mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
              />
            </svg>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No applications yet
            </h3>
            <p className="text-gray-600">
              Applications will appear here when volunteers apply to your
              request.
            </p>
          </div>
        </Card>
      ) : (
        <div className="grid gap-6">
          {applications.map((application) => (
            <Card key={application.volunteer._id}>
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-3">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {application.volunteer.name}
                    </h3>
                    {getStatusBadge(application.status)}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <span className="text-sm font-medium text-gray-700">
                        Email:
                      </span>
                      <p className="text-gray-600">
                        {application.volunteer.email}
                      </p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-700">
                        Applied on:
                      </span>
                      <p className="text-gray-600">
                        {application.appliedAt
                          ? new Date(application.appliedAt).toLocaleDateString()
                          : "Date not available"}
                      </p>
                    </div>
                  </div>

                  {application.volunteer.skills &&
                    application.volunteer.skills.length > 0 && (
                      <div className="mb-4">
                        <span className="text-sm font-medium text-gray-700 mr-2">
                          Skills:
                        </span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {application.volunteer.skills.map((skill, index) => (
                            <Badge key={index} variant="default" size="sm">
                              {skill}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                </div>
              </div>

              {application.status === "pending" && (
                <div className="flex justify-end gap-2 pt-4 border-t border-gray-200">
                  <Button
                    variant="danger"
                    size="sm"
                    onClick={() =>
                      handleStatusUpdate(application.volunteer._id, "rejected")
                    }
                    loading={updatingStatus === application.volunteer._id}
                    disabled={updatingStatus === application.volunteer._id}
                  >
                    Reject
                  </Button>
                  <Button
                    variant="success"
                    size="sm"
                    onClick={() =>
                      handleStatusUpdate(application.volunteer._id, "accepted")
                    }
                    loading={updatingStatus === application.volunteer._id}
                    disabled={updatingStatus === application.volunteer._id}
                  >
                    Accept
                  </Button>
                </div>
              )}
            </Card>
          ))}
        </div>
      )}

      <div className="mt-8 text-center">
        <Button variant="outline" onClick={() => navigate("/school/requests")}>
          Back to All Requests
        </Button>
      </div>
    </div>
  );
};

export default ManageApplications;
