{"name": "server", "version": "1.0.0", "main": "index.js", "type": "commonjs", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2", "server": "file:"}, "devDependencies": {"nodemon": "^3.1.9"}}