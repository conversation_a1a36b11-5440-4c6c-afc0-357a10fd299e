import React, { useState, useEffect } from "react";

// Props interface
export interface EditableSkillsProps {
  skills: string[];
  isEditing: boolean;
  onEdit: (name: string, skills: string[]) => void;
}

// Component for editing skills
const EditableSkills: React.FC<EditableSkillsProps> = ({
  skills,
  isEditing,
  onEdit,
}) => {
  const [currentSkills, setCurrentSkills] = useState<string[]>(skills || []);
  const [newSkill, setNewSkill] = useState("");

  // Update skills when props change
  useEffect(() => {
    setCurrentSkills(skills || []);
  }, [skills]);

  const handleAddSkill = () => {
    if (newSkill.trim() && !currentSkills.includes(newSkill.trim())) {
      const updatedSkills = [...currentSkills, newSkill.trim()];
      setCurrentSkills(updatedSkills);
      onEdit("skills", updatedSkills);
      setNewSkill("");
    }
  };

  const handleRemoveSkill = (skillToRemove: string) => {
    const updatedSkills = currentSkills.filter(
      (skill) => skill !== skillToRemove
    );
    setCurrentSkills(updatedSkills);
    onEdit("skills", updatedSkills);
  };

  // Handle key press to add skill on Enter
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddSkill();
    }
  };

  if (!isEditing) {
    return (
      <div className="mb-8">
        <div className="bg-gray-50 rounded-xl p-5 hover:bg-gray-100 transition-colors duration-200">
          <div className="flex items-center gap-2 mb-4">
            <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
            <h3 className="text-sm font-semibold text-gray-700 uppercase tracking-wide">
              Skills & Expertise
            </h3>
          </div>

          {currentSkills && currentSkills.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {currentSkills.map((skill, index) => (
                <div
                  key={index}
                  className="bg-gradient-to-r from-emerald-50 to-teal-50 border border-emerald-200 rounded-lg px-3 py-2 text-center"
                >
                  <span className="text-emerald-800 font-medium text-sm">
                    {skill}
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
              <p className="text-gray-400 italic">No skills specified</p>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="mb-8">
      <div className="bg-emerald-50 border-2 border-emerald-200 rounded-xl p-5">
        <div className="flex items-center gap-2 mb-4">
          <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
          <h3 className="text-sm font-semibold text-emerald-800 uppercase tracking-wide">
            Editing Skills & Expertise
          </h3>
        </div>

        {/* Current Skills */}
        {currentSkills && currentSkills.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-emerald-700 mb-2">
              Current Skills:
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {currentSkills.map((skill, index) => (
                <div
                  key={index}
                  className="bg-white border border-emerald-300 rounded-lg px-3 py-2 flex items-center justify-between group hover:bg-emerald-50 transition-colors duration-200"
                >
                  <span className="text-emerald-800 font-medium text-sm">
                    {skill}
                  </span>
                  <button
                    onClick={() => handleRemoveSkill(skill)}
                    className="text-red-500 hover:text-red-700 opacity-0 group-hover:opacity-100 transition-opacity duration-200 ml-2"
                    title="Remove skill"
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Add New Skill */}
        <div>
          <h4 className="text-sm font-medium text-emerald-700 mb-2">
            Add New Skill:
          </h4>
          <div className="flex gap-3">
            <input
              type="text"
              value={newSkill}
              onChange={(e) => setNewSkill(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Enter a skill (e.g., Teaching, Mathematics, Computer Science)"
              className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200"
            />
            <button
              onClick={handleAddSkill}
              disabled={!newSkill.trim()}
              className="px-6 py-3 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-200 flex items-center gap-2 font-medium"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
              Add
            </button>
          </div>
          <p className="text-xs text-emerald-600 mt-2">
            Press Enter to add a skill quickly
          </p>
        </div>
      </div>
    </div>
  );
};

export default EditableSkills;
