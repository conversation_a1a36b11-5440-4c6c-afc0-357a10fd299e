# RuralSangam – Requirements Draft (v0.1)

## 🎯 Overview

**RuralSangam** is a web platform connecting school representatives with volunteers.  
Schools can raise doubts, receive guidance, chat, and initiate meetings, while volunteers respond, share resources, and offer mentorship. The platform supports structured communication through forums, chat, and video meetings.

---

## 🧑‍🏫 School – Capabilities / Features

1. **Create Threads for Help**
   - Post questions or requests for help on specific topics.
   - Each thread includes title, description, and optional file attachments.

2. **Track Interactions**
   - Receive notifications for:
     - Volunteer responses
     - Meeting invites
     - Chat messages
   - Follow-up alerts for unanswered or ongoing threads.

3. **Chat with Volunteers**
   - Can start chats with volunteers who have responded to their threads.
   - _[Maybe]_ Ability to directly search for and message volunteers from a list.

4. **Participate in Other Threads**
   - _[Maybe]_ Should schools be able to view and reply to threads created by other schools?

5. **Video Meetings**
   - Can schedule or initiate a video meeting with a volunteer (when both agree).

---

## 🤝 Volunteer – Capabilities / Features

1. **Receive & Respond to Messages**
   - Get notified when assigned or tagged in a thread.
   - Reply with text and attach relevant resources.

2. **Initiate Video Calls**
   - Can schedule or start video meetings from chat.

3. **Forum Participation**
   - Can view all public threads and choose to respond.
   - _[Maybe]_ Convert a thread into a dedicated chat for deeper 1-on-1 help (if the school agrees).

---

## 🧪 Optional / Potential Features

1. **School Verification**
   - _[Not Fully Sure]_ Introduce a verification system to ensure school authenticity:
     - Document upload during signup
     - Manual admin approval

2. **Volunteer Profiles & Badges**
   - Volunteers have public profiles with reviews or activity stats.
   - _[Optional]_ Earn badges or points for helping a number of schools.

3. **Admin Tools**
   - _[Optional]_ Admin panel with the ability to:
     - View and manage users
     - Delete abusive threads
     - Moderate general activity

4. **Thread Tags / Categories**
   - Tag threads by subject (e.g., Math, English) or skill type (e.g., Career, Scholarships, Personal Development).
   - Enables filtering and easier discovery.

5. **Common Discussion Wall**
   - _[To Be Decided]_ A shared space for announcements, general questions, or event updates.

---

